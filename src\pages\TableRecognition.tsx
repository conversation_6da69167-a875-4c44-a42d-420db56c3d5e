import React, { useState } from 'react';
import { Layout, Upload, message, Tabs, Card } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { recognizeTable } from '../services/recognition';

const { Content } = Layout;
const { Dragger } = Upload;
const { TabPane } = Tabs;

const StyledContent = styled(Content)`
  padding: 24px;
  min-height: 100vh;
  background: #fff;
`;

const Container = styled.div`
  display: flex;
  gap: 24px;
  height: calc(100vh - 48px);
  min-width: 700px;
`;

const LeftPanel = styled.div`
  width: 680px;
  min-width: 320px;
  display: flex;
  flex-direction: column;
`;

const RightPanel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  margin-top: 16px;
`;

const TableHtml = styled.div`
  table, th, td {
    border: 1px solid #ccc;
    border-collapse: collapse;
  }
  th, td {
    padding: 8px;
  }
`;

const TableRecognition: React.FC = () => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [recognitionResult, setRecognitionResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleUpload = async (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }

    setLoading(true);
    try {
      const result = await recognizeTable(file);
      setRecognitionResult(result);
      setImageUrl(URL.createObjectURL(file));
      message.success('识别成功');
    } catch (error) {
      message.error('识别失败，请重试');
    } finally {
      setLoading(false);
    }
    return false;
  };

  return (
    <StyledContent>
      <Container>
        <LeftPanel>
          <Card title="上传图片" loading={loading}>
            <Dragger
              accept="image/*"
              beforeUpload={handleUpload}
              showUploadList={false}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
              <p className="ant-upload-hint">支持单个图片上传</p>
            </Dragger>
            {imageUrl && <PreviewImage src={imageUrl} alt="预览" />}
          </Card>
        </LeftPanel>

        <RightPanel>
          <Card title="识别结果">
            <Tabs defaultActiveKey="1">
              <TabPane tab="表格" key="1">
                {recognitionResult && (
                  <TableHtml
                    className="table-html"
                    dangerouslySetInnerHTML={{
                      __html: recognitionResult.result.text,
                    }}
                  />
                )}
              </TabPane>
              <TabPane tab="JSON" key="2">
                <div style={{ maxHeight: 400, maxWidth: '100%', overflow: 'auto' }}>
                  <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
                    {recognitionResult &&
                      JSON.stringify(recognitionResult, null, 2)}
                  </pre>
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </RightPanel>
      </Container>
    </StyledContent>
  );
};

export default TableRecognition; 