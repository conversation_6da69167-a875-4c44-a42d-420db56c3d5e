import React, { useState } from 'react';
import { Layout, Upload, message, Tabs, Card } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { recognizePage, PageRecognitionResponse, PageRecognitionResult } from '../services/recognition';
import ReactMarkdown from 'react-markdown';

const { Content } = Layout;
const { Dragger } = Upload;
const { TabPane } = Tabs;

const StyledContent = styled(Content)`
  padding: 24px;
  min-height: 100vh;
  background: #fff;
`;

const Container = styled.div`
  display: flex;
  gap: 24px;
  height: calc(100vh - 48px);
  min-width: 700px;
`;

const LeftPanel = styled.div`
  width: 680px;
  min-width: 320px;
  display: flex;
  flex-direction: column;
`;

const RightPanel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  margin-top: 16px;
`;

const MarkdownBox = styled.div`
  padding: 8px;
  background: #fafbfc;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
`;

// 根据标签类型转换为 Markdown 格式
const convertToMarkdown = (item: PageRecognitionResult): string => {
  const { label, text } = item;

  switch (label.toLowerCase()) {
    case 'title':
    case 'heading':
    case 'h1':
      return `# ${text}\n\n`;

    case 'subtitle':
    case 'subheading':
    case 'h2':
      return `## ${text}\n\n`;

    case 'h3':
      return `### ${text}\n\n`;

    case 'h4':
      return `#### ${text}\n\n`;

    case 'h5':
      return `##### ${text}\n\n`;

    case 'h6':
      return `###### ${text}\n\n`;

    case 'para':
    case 'paragraph':
    case 'text':
      return `${text}\n\n`;

    case 'list':
    case 'list-item':
      // 如果文本已经包含列表标记，直接使用；否则添加
      if (text.trim().startsWith('-') || text.trim().startsWith('*') || text.trim().startsWith('+')) {
        return `${text}\n`;
      }
      return `- ${text}\n`;

    case 'ordered-list':
    case 'numbered-list':
      // 如果文本已经包含数字标记，直接使用；否则添加
      if (/^\d+\./.test(text.trim())) {
        return `${text}\n`;
      }
      return `1. ${text}\n`;

    case 'quote':
    case 'blockquote':
      return `> ${text}\n\n`;

    case 'code':
    case 'code-block':
      return `\`\`\`\n${text}\n\`\`\`\n\n`;

    case 'inline-code':
      return `\`${text}\``;

    case 'table':
      // 如果文本已经是表格格式，直接使用；否则简单处理
      if (text.includes('|')) {
        return `${text}\n\n`;
      }
      return `| ${text} |\n|---|\n\n`;

    case 'emphasis':
    case 'italic':
      return `*${text}*`;

    case 'strong':
    case 'bold':
      return `**${text}**`;

    case 'link':
      return `[${text}](${text})`;

    case 'image':
      return `![${text}](${text})\n\n`;

    case 'hr':
    case 'separator':
      return `---\n\n`;

    default:
      // 默认作为段落处理
      return `${text}\n\n`;
  }
};

const PageRecognition: React.FC = () => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [recognitionResult, setRecognitionResult] = useState<PageRecognitionResponse | null>(null);
  const [loading, setLoading] = useState(false);

  const handleUpload = async (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }
    setLoading(true);
    try {
      const result = await recognizePage(file);
      setRecognitionResult(result);
      setImageUrl(URL.createObjectURL(file));
      message.success('识别成功');
    } catch (error) {
      message.error('识别失败，请重试');
    } finally {
      setLoading(false);
    }
    return false;
  };

  return (
    <StyledContent>
      <Container>
        <LeftPanel>
          <Card title="上传图片" loading={loading}>
            <Dragger
              accept="image/*"
              beforeUpload={handleUpload}
              showUploadList={false}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
              <p className="ant-upload-hint">支持单个图片上传</p>
            </Dragger>
            {imageUrl && <PreviewImage src={imageUrl} alt="预览" />}
          </Card>
        </LeftPanel>
        <RightPanel>
          <Card title="识别结果">
            <Tabs defaultActiveKey="1">
              <TabPane tab="Markdown" key="1">
                {recognitionResult && (
                  <MarkdownBox>
                    <ReactMarkdown>
                      {recognitionResult.results.map(item => convertToMarkdown(item)).join('')}
                    </ReactMarkdown>
                  </MarkdownBox>
                )}
              </TabPane>
              <TabPane tab="JSON" key="2">
                <div style={{ maxHeight: 400, maxWidth: '100%', overflow: 'auto' }}>
                  <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
                    {recognitionResult && JSON.stringify(recognitionResult, null, 2)}
                  </pre>
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </RightPanel>
      </Container>
    </StyledContent>
  );
};

export default PageRecognition; 