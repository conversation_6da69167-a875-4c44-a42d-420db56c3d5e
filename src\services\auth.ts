import axios from 'axios';

const API_URL = 'https://bate.ele-arch.com/api';

export interface LoginResponse {
  result: {
    accessToken: string;
    encryptedAccessToken: string;
    expireInSeconds: number;
    userId: number;
    roles: string;
    userName: string;
  };
  success: boolean;
  error: any;
}

export const login = async (username: string, password: string): Promise<LoginResponse> => {
  const response = await axios.post(`${API_URL}/TokenAuth/Authenticate`, {
    userNameOrEmailAddress: window.btoa(username),
    password: window.btoa(password),
    IsEncrypt: true
  });
  return response.data;
};

export const setToken = (token: string) => {
  localStorage.setItem('token', token);
};

export const getToken = () => {
  return localStorage.getItem('token');
};

export const removeToken = () => {
  localStorage.removeItem('token');
};

export const isAuthenticated = () => {
  return !!getToken();
}; 