import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import Login from './pages/Login';
import TableRecognition from './pages/TableRecognition';
import MainLayout from './layouts/MainLayout';
import { isAuthenticated } from './services/auth';
import PageRecognition from './pages/PageRecognition';

const PrivateRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return isAuthenticated() ? <>{children}</> : <Navigate to="/login" />;
};

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route
            path="/table-recognition"
            element={
              <PrivateRoute>
                <MainLayout>
                  <TableRecognition />
                </MainLayout>
              </PrivateRoute>
            }
          />
          <Route
            path="/page-recognition"
            element={
              <PrivateRoute>
                <MainLayout>
                  <PageRecognition />
                </MainLayout>
              </PrivateRoute>
            }
          />
          <Route path="/" element={<Navigate to="/table-recognition" />} />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;
