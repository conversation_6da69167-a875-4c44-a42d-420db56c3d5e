import axios from 'axios';

const API_URL = 'http://localhost:8000/api';

export interface RecognitionResponse {
  result: {
    label: string;
    text: string;
  };
}

export const recognizeTable = async (file: File): Promise<RecognitionResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('element_type', 'table');

  const response = await axios.post(`${API_URL}/element-recognition`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export interface PageRecognitionResult {
  label: string;
  bbox: number[];
  text: string;
}

export interface PageRecognitionResponse {
  results: PageRecognitionResult[];
}

export const recognizePage = async (file: File): Promise<PageRecognitionResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await axios.post(`${API_URL}/page-recognition`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
}; 