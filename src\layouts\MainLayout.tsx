import React from 'react';
import { Layout, Menu } from 'antd';
import { TableOutlined, LogoutOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { removeToken } from '../services/auth';

const { Header, Sider, Content } = Layout;

const StyledLayout = styled(Layout)`
  min-height: 100vh;
`;

const StyledHeader = styled(Header)`
  background: #fff;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Logo = styled.div`
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
`;

const StyledSider = styled(Sider)`
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
`;

const MainLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    removeToken();
    navigate('/login');
  };

  return (
    <StyledLayout>
      <StyledHeader>
        <Logo>表格识别系统</Logo>
        <Menu mode="horizontal" selectedKeys={[]}>
          <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
            退出登录
          </Menu.Item>
        </Menu>
      </StyledHeader>
      <Layout>
        <StyledSider width={200}>
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            style={{ height: '100%', borderRight: 0 }}
          >
            <Menu.Item
              key="/table-recognition"
              icon={<TableOutlined />}
              onClick={() => navigate('/table-recognition')}
            >
              表格识别
            </Menu.Item>
            <Menu.Item
              key="/page-recognition"
              icon={<TableOutlined />}
              onClick={() => navigate('/page-recognition')}
            >
              全文识别
            </Menu.Item>
          </Menu>
        </StyledSider>
        <Layout style={{ padding: '0 24px 24px' }}>
          <Content>{children}</Content>
        </Layout>
      </Layout>
    </StyledLayout>
  );
};

export default MainLayout; 